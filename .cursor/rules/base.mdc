---
description: 
globs: 
alwaysApply: true
---
# AI推理平台 Cursor Rules

## 项目概述

AI推理平台是一个基于FastAPI、PostgreSQL、Redis的插件化AI推理系统，采用分层架构设计：
- API应用层：处理HTTP请求和响应
- 核心业务逻辑层：实现业务逻辑和服务编排
- 推理引擎层：负责模型加载和推理执行
- 基础设施层：提供数据持久化和缓存服务

## 代码风格规范

### 命名规范
- **模块/包名**：全小写，短名称，不使用下划线 (例如: `app`, `core`, `utils`)
- **类名**：大驼峰命名法 (例如: `TaskScheduler`, `PluginLoader`)
- **函数/方法名**：蛇形命名法 (例如: `get_task_status`, `load_plugin`)
- **变量名**：蛇形命名法 (例如: `task_id`, `plugin_code`)
- **常量名**：全大写，单词间用下划线分隔 (例如: `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`)

### 导入顺序
遵循以下顺序组织导入语句：
1. 标准库导入
2. 相关第三方导入
3. 本地应用/库特定导入
每组之间用空行分隔。

```python
# 标准库
import os
import asyncio
from typing import List, Optional

# 第三方库
from fastapi import APIRouter, Depends, HTTPException
import sqlalchemy as sa

# 本地应用
from app.core.task import TaskService
from app.schemas.plugin import PluginCreate
```

### 文档字符串
- 使用多行字符串文档
- 函数/方法文档包含功能描述、参数、返回值和异常说明
- 参数类型通过类型提示（type hints）指定

```python
async def get_plugin(plugin_id: int) -> Plugin:
    """
    获取指定ID的插件信息
    
    Args:
        plugin_id: 插件ID
        
    Returns:
        Plugin: 插件对象
        
    Raises:
        PluginNotFoundError: 插件不存在时抛出
    """
    # 函数实现
```

## 项目结构指南

### 主要目录职责
- **app/api**: API路由定义，按功能模块和版本组织
- **app/core**: 核心业务逻辑，包含各领域服务实现
- **app/plugins**: 插件管理和执行相关
- **app/models**: 数据库模型定义
- **app/schemas**: 请求/响应数据模式定义
- **app/db**: 数据库连接和会话管理
- **app/utils**: 通用工具函数
- **app/config**: 应用配置管理
- **app/middleware**: HTTP中间件实现

### 模块边界
- API层只负责HTTP请求处理，不包含业务逻辑
- 核心业务逻辑层通过服务类实现，不直接处理HTTP请求
- 数据访问逻辑通过数据库模型层实现，不在业务逻辑中直接构建SQL
- 工具函数应该是通用的，不应包含业务逻辑

## 插件开发规范

### 插件目录结构
```
/plugins/{plugin_code}/{version}/
├── model.onnx         # 模型文件
├── config.yaml        # 配置文件
└── plugin.py          # Python逻辑文件
```

### 插件接口
- 插件必须实现`BasePlugin`抽象类定义的接口
- 必须实现的方法：`process`

```python
class MyPlugin():
    async def process(self, input_context: Dict[str, Any], engine: Any) -> dict:
        # 处理逻辑
        return processed_data
    
```

### 配置文件规范
- 使用YAML格式
- 必须包含`metadata`, `input`, `output`, `parameters`四个顶级字段

## API开发规范

### 路由组织
- 按功能模块组织路由，放在`app/api/v{version}/`目录下
- 使用APIRouter创建和组织路由

### 请求/响应规范
- 所有请求/响应模式通过Pydantic模型定义
- 响应统一使用包装模式，成功返回data字段，失败返回error字段

```python
# 成功响应
{
  "code": 0,              // 状态码，0表示成功，其他表示错误
  "success": true,        // 是否成功
  "message": "操作成功",   // 操作结果描述
  "data": {               // 响应数据，可以是对象、数组或null
    // 具体数据内容
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0", // 请求追踪ID
  "timestamp": "1744184311000" // 响应时间戳（毫秒）
}
# 失败响应
{
  "code": 30001,
  "success": false,
  "message": "任务不存在",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 数据库交互规范

### 模型定义
- 使用SQLAlchemy ORM定义数据库模型
- 模型类继承`Base`基类
- 使用`__tablename__`指定表名
- 列名使用蛇形命名，映射到数据库字段

```python
class Plugin(Base):
    __tablename__ = "plugin"
    
    id = Column(BigInteger, primary_key=True)
    plugin_code = Column(String(100), nullable=False)
    plugin_version = Column(String(20), nullable=False)
    # 其他字段...
```

### 查询构建
- 使用异步SQLAlchemy接口
- 优先使用ORM查询，复杂查询可使用Core表达式或原生SQL

```python
# 使用ORM查询
result = await db.execute(
    select(Plugin).where(Plugin.id == plugin_id)
)
plugin = result.scalars().first()
```

## 异步编程规范

### 异步函数命名
- 异步函数不需要特殊前缀或后缀，但必须使用`async`关键字定义
- 异步函数内部应避免阻塞操作，需要时使用`run_in_executor`

### 异步上下文使用
- 使用`async with`管理异步上下文
- 使用`asynccontextmanager`创建异步上下文管理器

```python
@asynccontextmanager
async def get_db_session():
    async with async_session() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
```

## 错误处理和日志规范

### 异常处理
- 定义和使用自定义异常类，继承自`Exception`
- 在API层使用全局异常处理器统一处理异常
- 内部服务抛出具体异常，由上层处理

### 日志记录
- 使用项目定义的logger进行日志记录
- 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
- 关键操作必须有INFO级别以上的日志
- 异常必须记录ERROR级别日志，并包含异常详情

```python
from app.log import logger

try:
    # 业务操作
    logger.info(f"任务 {task_id} 创建成功")
except Exception as e:
    logger.error(f"任务 {task_id} 创建失败: {str(e)}", exc_info=True)
    raise
```
