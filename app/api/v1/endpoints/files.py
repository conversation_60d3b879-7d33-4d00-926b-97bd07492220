"""
文件管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, UploadFile, File as FastAPIFile, Form, Query, Path, Body
from fastapi.responses import JSONResponse

from app.schemas.base import BaseResponse
from app.schemas.file import (
    File, FileUploadRequest, FileDownloadRequest, FileDeleteRequest, 
    TempUrlRequest, TempUrlResponse
)
from app.core.file import file_manager

router = APIRouter()


@router.post("/upload", response_model=BaseResponse[File], summary="上传文件")
async def upload_file(
    file: UploadFile = FastAPIFile(...),
    request: FileUploadRequest = Depends()
):
    """
    上传文件
    
    - **file**: 要上传的文件
    - **request**: 上传参数
        - **fileType**: 文件类型，可选
        - **mediaType**: 媒体类型，可选
        - **generateMetadata**: 是否生成元数据，默认为True
    """
    result = await file_manager.upload_file(
        file=file,
        file_type=request.file_type,
        generate_metadata=request.generate_metadata
    )
    
    return BaseResponse(
        code=0,
        success=True,
        message="文件上传成功",
        data=result
    )


@router.post("/download", response_model=BaseResponse[File], summary="从URL下载文件")
async def download_file(
    request: FileDownloadRequest
):
    """
    从URL下载文件
    
    - **request**: 下载参数
        - **url**: 文件URL
        - **fileType**: 文件类型
    """
    result = await file_manager.download_from_url(
        url=request.url,
        file_type=request.file_type
    )
    
    return BaseResponse(
        code=0,
        success=True,
        message="文件下载成功",
        data=result
    )


@router.get("/{file_id}", response_model=BaseResponse[File], summary="获取文件详情")
async def get_file(
    file_id: int = Path(..., description="文件ID")
):
    """
    获取文件详情
    
    - **file_id**: 文件ID
    """
    result = await file_manager.get_file(file_id)
    
    return BaseResponse(
        code=0,
        success=True,
        message="获取文件详情成功",
        data=result
    )


@router.delete("/{file_id}", response_model=BaseResponse[bool], summary="删除文件")
async def delete_file(
    file_id: int = Path(..., description="文件ID"),
    request: FileDeleteRequest = Body(default_factory=FileDeleteRequest)
):
    """
    删除文件（逻辑删除）
    
    - **file_id**: 文件ID
    """
    result = await file_manager.delete_file(file_id)
    
    return BaseResponse(
        code=0,
        success=True,
        message="文件删除成功" if result else "文件删除失败",
        data=result
    )


@router.get("/{file_id}/sts", response_model=BaseResponse[str], summary="生成文件临时访问URL")
async def generate_temp_url(
    file_id: int = Path(..., description="文件ID"),
    expires: int = Query(3600, description="过期时间(秒)", ge=1, le=86400)
):
    """
    生成文件临时访问URL
    
    - **file_id**: 文件ID
    - **expires**: URL有效期（秒），默认3600秒，范围1-86400
    """
    temp_url_response = await file_manager.generate_temp_url(
        file_id=file_id,
        expires_in=expires
    )
    
    return BaseResponse(
        code=0,
        success=True,
        message="生成临时访问URL成功",
        data=temp_url_response.url
    ) 