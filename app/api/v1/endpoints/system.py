"""
系统管理API
提供系统监控、状态查询等功能
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
from app.core.task.scheduler import scheduler

router = APIRouter()


@router.get("/engine-pool/stats", summary="获取引擎池统计信息")
async def get_engine_pool_stats():
    """
    获取引擎池统计信息
    
    Returns:
        引擎池统计数据，包含缓存命中率、活跃引擎数等信息
    """
    try:
        from app.plugins.engines import get_engine_pool
        engine_pool = await get_engine_pool()
        return engine_pool.get_stats()
    except ImportError:
        raise HTTPException(status_code=404, detail="引擎池模块未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取引擎池统计信息失败: {str(e)}")


@router.get("/health", summary="基础健康检查")
async def health_check():
    """基础健康检查"""
    return {"status": "ok", "message": "服务正常运行"}


@router.get("/health/scheduler", summary="调度器健康检查")
async def scheduler_health_check():
    """
    调度器健康检查

    Returns:
        调度器详细健康状态信息
    """
    try:
        health_status = await scheduler.get_health_status()
        return health_status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调度器健康状态失败: {str(e)}")


@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check():
    """
    详细健康检查

    Returns:
        系统整体健康状态，包括调度器、数据库、Redis等
    """
    try:
        scheduler_health = await scheduler.get_health_status()

        return {
            "status": "ok" if scheduler_health.get("healthy", False) else "error",
            "scheduler": scheduler_health,
            "timestamp": scheduler_health.get("uptime_seconds", 0)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "scheduler": {"healthy": False, "error": str(e)}
        }