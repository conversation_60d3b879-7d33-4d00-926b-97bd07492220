-- 移除 result_cache 表中的 use_count 字段
-- 执行时间：2025-07-03
-- 说明：移除缓存使用次数字段，简化缓存逻辑并避免并发更新问题

-- 开始事务
BEGIN;

-- 检查字段是否存在
DO $$
BEGIN
    -- 检查 use_count 字段是否存在
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'result_cache' 
        AND column_name = 'use_count'
    ) THEN
        -- 移除 use_count 字段
        ALTER TABLE result_cache DROP COLUMN use_count;
        RAISE NOTICE '已成功移除 result_cache.use_count 字段';
    ELSE
        RAISE NOTICE 'result_cache.use_count 字段不存在，跳过删除';
    END IF;
END $$;

-- 提交事务
COMMIT;

-- 验证字段已被移除
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'result_cache' 
ORDER BY ordinal_position;
