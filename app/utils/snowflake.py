"""
雪花ID生成器

Twitter的Snowflake算法生成的ID结构：
- 1位符号位，始终为0（正数）
- 41位时间戳（毫秒级）
- 5位数据中心ID
- 5位工作节点ID
- 12位序列号（毫秒内）

提供全局唯一、粗略有序、高性能生成的分布式ID
"""
import time
import threading
import asyncio
from typing import Optional

from app.log import logger


class SnowflakeGenerator:
    """雪花ID生成器"""

    # 位数定义 - 扩展worker_id位数以支持更多worker
    SEQUENCE_BITS = 12
    WORKER_ID_BITS = 10  # 扩展到10位，支持0-1023个worker

    # 最大值定义
    MAX_WORKER_ID = -1 ^ (-1 << WORKER_ID_BITS)  # 1023
    MAX_DATA_CENTER_ID = 0  # 不使用数据中心ID
    MAX_SEQUENCE = -1 ^ (-1 << SEQUENCE_BITS)  # 4095

    # 移位定义
    WORKER_ID_SHIFT = SEQUENCE_BITS
    TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS  # 简化后的移位

    # 起始时间戳（2023-01-01 00:00:00）
    EPOCH = 1672502400000

    def __init__(self, worker_id=0):
        """
        初始化雪花ID生成器

        Args:
            worker_id: 工作节点ID (0~1023)
        """
        # 校验参数
        if worker_id > self.MAX_WORKER_ID or worker_id < 0:
            raise ValueError(f"工作节点ID必须在0和{self.MAX_WORKER_ID}之间")

        self.worker_id = worker_id
        self.sequence = 0
        self.last_timestamp = -1
        self.lock = threading.Lock()
        self._async_lock: Optional[asyncio.Lock] = None

    def _generate_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)

    def _next_sequence(self, timestamp) -> int:
        """获取下一个序列值"""
        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.MAX_SEQUENCE
            if self.sequence == 0:
                # 当前毫秒序列用完，等待下一毫秒
                timestamp = self._wait_next_millis(timestamp)
        else:
            self.sequence = 0
        self.last_timestamp = timestamp
        return self.sequence

    def _wait_next_millis(self, last_timestamp) -> int:
        """等待到下一毫秒"""
        timestamp = self._generate_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._generate_timestamp()
        return timestamp

    def _get_async_lock(self) -> asyncio.Lock:
        """获取异步锁，延迟初始化以避免事件循环问题"""
        if self._async_lock is None:
            self._async_lock = asyncio.Lock()
        return self._async_lock

    def next_id(self) -> int:
        """生成下一个雪花ID（同步版本，用于向后兼容）"""
        with self.lock:
            return self._generate_id()

    async def next_id_async(self) -> int:
        """生成下一个雪花ID（异步版本，推荐使用）"""
        async with self._get_async_lock():
            return self._generate_id()

    def _generate_id(self) -> int:
        """内部ID生成逻辑"""
        timestamp = self._generate_timestamp()

        # 时钟回拨检测
        if timestamp < self.last_timestamp:
            logger.error(f"时钟回拨异常，拒绝生成ID，回拨 {self.last_timestamp - timestamp} 毫秒")
            raise RuntimeError(
                f"时钟回拨异常，拒绝生成ID，"
                f"当前时间戳: {timestamp}, 上次时间戳: {self.last_timestamp}"
            )

        self.sequence = self._next_sequence(timestamp)

        return (
            ((timestamp - self.EPOCH) << self.TIMESTAMP_SHIFT)
            | (self.worker_id << self.WORKER_ID_SHIFT)
            | self.sequence
        )


# 全局雪花ID生成器实例
snowflake: Optional[SnowflakeGenerator] = None


async def init_snowflake_worker():
    """
    初始化雪花ID生成器，使用统一的Worker ID管理器
    """
    global snowflake
    if snowflake is not None:
        logger.info("雪花ID生成器已初始化，跳过")
        return

    try:
        from app.utils.worker import worker_manager

        # 从统一的Worker管理器获取数字worker_id
        worker_id = await worker_manager.get_worker_id()

        snowflake = SnowflakeGenerator(worker_id=worker_id)

        worker_name = await worker_manager.get_worker_name()
        logger.info(f"雪花ID生成器初始化成功，工作节点ID: {worker_name}")

    except Exception as e:
        logger.error(f"雪花ID生成器初始化失败: {e}", exc_info=True)
        # 在初始化失败时，使用默认的worker_id=0作为备用方案，但发出严重警告
        if snowflake is None:
            snowflake = SnowflakeGenerator(worker_id=0)
            logger.warning("雪花ID生成器使用默认worker_id=0进行初始化，可能存在ID冲突风险！")


async def get_snowflake_id_async() -> int:
    """获取雪花ID（异步）"""
    if snowflake is None:
        raise RuntimeError("雪花ID生成器未初始化，请在应用启动时调用 init_snowflake_worker")
    return await snowflake.next_id_async() 