"""
Gunicorn 配置文件
用于生产环境中以 Gunicorn 方式部署 FastAPI 应用
"""
import multiprocessing
import os
from app.config.settings import settings

# 工作进程数 - 一般建议设置为 (2 x $num_cores) + 1
# workers = int(os.getenv("GUNICORN_WORKERS", min(multiprocessing.cpu_count() * 2 + 1, 8)))
workers = 11

# 每个工作进程的线程数
threads = int(os.getenv("GUNICORN_THREADS", 1))

# 绑定的IP和端口
bind = f"0.0.0.0:{settings.APP_PORT}"

# 工作模式
worker_class = "uvicorn.workers.UvicornWorker"

# 超时设置 (秒)
timeout = 120

# 调试模式
reload = settings.DEBUG

# 日志配置
loglevel = "debug" if settings.DEBUG else "info"
accesslog = "-"  # 标准输出
errorlog = "-"   # 标准错误输出

# 预加载应用 (在fork worker前加载应用代码，能够节省内存)
preload_app = not settings.DEBUG

# 优雅的关闭超时 (秒)
graceful_timeout = 30

# keepalive超时 (秒)
keepalive = 5

# 最大请求数 (worker 会在处理了这么多请求后重启)
max_requests = 1000
max_requests_jitter = 100  # 添加随机抖动，避免所有worker同时重启 